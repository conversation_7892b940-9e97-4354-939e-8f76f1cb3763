//
//  AppioWidgetPreviewView.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI
import WidgetKit

struct AppioWidgetPreviewView: View {
    private let services = StorageManager.services

    var body: some View {
        ZStack(alignment: .leading) {
            if services.isEmpty {
                PreviewNoServicesView()
            } else {
                PreviewServicesLogosView(services: services)
            }
        }
        .padding()
    }
}

struct PreviewNoServicesView: View {
    var body: some View {
        VStack(spacing: 8) {
            Image("LaunchImage")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .opacity(0.6)
        }
    }
}

struct PreviewServicesLogosView: View {
    let services: [ServiceEntity]

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Services")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 4), spacing: 8) {
                ForEach(services.prefix(8), id: \.id) { service in
                    VStack(spacing: 2) {
                        if let logoURL = URL(string: service.logoURL) {
                            Image.cached(url: logoURL)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 24, height: 24)
                                .clipShape(RoundedRectangle(cornerRadius: 4))
                        } else {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(.gray.opacity(0.3))
                                .frame(width: 24, height: 24)
                                .overlay {
                                    Image(systemName: "app")
                                        .font(.system(size: 12))
                                        .foregroundColor(.secondary)
                                }
                        }

                        Text(service.title)
                            .font(.system(size: 8))
                            .lineLimit(1)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
    }
}

#if DEBUG
#Preview("Preview Widget", as: .systemMedium) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}

#Preview("Preview Widget Small", as: .systemSmall) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}

#Preview("Preview Widget Large", as: .systemLarge) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}
#endif
